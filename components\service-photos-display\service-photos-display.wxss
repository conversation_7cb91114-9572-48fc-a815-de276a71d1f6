.service-photos-display {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.photos-section {
  margin-bottom: 40rpx;
}

.photos-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 12rpx;
}

.photo-count {
  font-size: 26rpx;
  color: #666;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.photo-item {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
}

.loading-state {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.no-photos {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.no-photos-text {
  font-size: 28rpx;
  color: #999;
}
