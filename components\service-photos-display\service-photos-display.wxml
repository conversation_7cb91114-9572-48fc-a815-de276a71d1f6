<view class="service-photos-display" wx:if="{{hasServicePhotos && servicePhotos}}">
  <!-- 服务前照片 -->
  <view class="photos-section" wx:if="{{servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0}}">
    <view class="section-title">
      <text class="title-text">服务前照片</text>
      <text class="photo-count">({{servicePhotos.beforePhotos.length}}张)</text>
    </view>
    <view class="photos-grid">
      <image
        wx:for="{{servicePhotos.beforePhotos}}"
        wx:key="index"
        src="{{item}}"
        class="photo-item"
        mode="aspectFill"
        bindtap="previewBeforePhoto"
        data-current="{{item}}"
      ></image>
    </view>
  </view>

  <!-- 服务后照片 -->
  <view class="photos-section" wx:if="{{servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0}}">
    <view class="section-title">
      <text class="title-text">服务后照片</text>
      <text class="photo-count">({{servicePhotos.afterPhotos.length}}张)</text>
    </view>
    <view class="photos-grid">
      <image
        wx:for="{{servicePhotos.afterPhotos}}"
        wx:key="index"
        src="{{item}}"
        class="photo-item"
        mode="aspectFill"
        bindtap="previewAfterPhoto"
        data-current="{{item}}"
      ></image>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-state" wx:if="{{loading}}">
  <text class="loading-text">正在加载服务照片...</text>
</view>

<!-- 无照片状态 -->
<view class="no-photos" wx:if="{{!hasServicePhotos && !loading && servicePhotos !== null}}">
  <text class="no-photos-text">暂无服务照片</text>
</view>
