.module-container {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 模块标题 */
.module-title {
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 模块内容 */
.module-content {
  padding: 0;
}

/* 加载状态 */
.module-loading {
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #2f83ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.module-empty {
  padding: 80rpx 40rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.empty-action {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background-color: #f8f9fa;
  border-radius: 50rpx;
  border: 1rpx solid #e9ecef;
  min-width: 200rpx;
}

.action-text {
  font-size: 28rpx;
  color: #2f83ff;
  margin-right: 10rpx;
}

.action-arrow {
  font-size: 24rpx;
  color: #2f83ff;
}

/* 不同模块类型的样式 */
.module-container.additional-services .module-title {
  background-color: #e8f4fd;
}

.module-container.additional-services .title-text {
  color: #1976d2;
}

.module-container.service-duration .module-title {
  background-color: #fff3e0;
}

.module-container.service-duration .title-text {
  color: #f57c00;
}

.module-container.special-note .module-title {
  background-color: #fce4ec;
}

.module-container.special-note .title-text {
  color: #c2185b;
}

.module-container.service-photos .module-title {
  background-color: #e8f5e8;
}

.module-container.service-photos .title-text {
  color: #388e3c;
}

.module-container.service-review .module-title {
  background-color: #fff8e1;
}

.module-container.service-review .title-text {
  color: #f9a825;
}
