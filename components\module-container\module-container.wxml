<view class="module-container {{moduleType}}">
  <!-- 模块标题 -->
  <view class="module-title">
    <text class="title-text">{{title}}</text>
  </view>

  <!-- 加载状态 -->
  <view class="module-loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 有内容时显示内容 -->
  <view class="module-content" wx:elif="{{hasContent}}">
    <slot></slot>
  </view>

  <!-- 无内容时显示空状态 -->
  <view class="module-empty" wx:else>
    <view class="empty-content">
      <view class="empty-icon">{{emptyIcon}}</view>
      <text class="empty-text">{{emptyText}}</text>
      <view class="empty-action" wx:if="{{showAction}}" bindtap="onActionClick">
        <text class="action-text">{{actionText}}</text>
        <view class="action-arrow">></view>
      </view>
    </view>
  </view>
</view>
