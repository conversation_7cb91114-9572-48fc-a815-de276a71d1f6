<view class="container">
  <!-- 订单基本信息组件 -->
  <order-basic-info
    orderDetail="{{orderDetail}}"
    bind:reschedule="reschedule"
    bind:editAddress="editServiceAddress"
    bind:openNavigation="openNavigation"
  />

  <!-- 服务评价组件 -->
  <service-review
    orderDetail="{{orderDetail}}"
    reviewData="{{reviewData}}"
    hasReview="{{hasReview}}"
    reviewLoading="{{reviewLoading}}"
    bind:viewReview="viewReview"
  />

  <!-- 服务照片显示组件 -->
  <service-photos-display
    wx:if="{{orderDetail.status === '已完成' || orderDetail.status === '已评价'}}"
    servicePhotos="{{servicePhotos}}"
    hasServicePhotos="{{hasServicePhotos}}"
    loading="{{servicePhotosLoading}}"
  />

  <!-- 服务时长统计组件 -->
  <service-duration
    orderDetail="{{orderDetail}}"
    serviceDurationRecords="{{serviceDurationRecords}}"
    allAdditionalServices="{{allAdditionalServices}}"
    serviceDurationStatistics="{{serviceDurationStatistics}}"
    showServiceDuration="{{showServiceDuration}}"
    bind:toggleServiceDuration="toggleServiceDuration"
    bind:startMainService="startMainService"
    bind:startAdditionalService="startAdditionalService"
    bind:endServiceDuration="endServiceDuration"
  />

  <!-- 待确认追加服务组件 -->
  <pending-additional-services
    pendingAdditionalServices="{{pendingAdditionalServices}}"
    bind:confirmService="confirmAdditionalService"
    bind:rejectService="rejectAdditionalService"
  />

  <!-- 追加服务记录组件 -->
  <additional-service-records
    confirmedAdditionalServiceOrders="{{confirmedAdditionalServiceOrders}}"
    bind:confirmService="confirmAdditionalService"
    bind:rejectService="rejectAdditionalService"
  />

  <!-- 特殊情况说明组件 -->
  <special-note-display
    orderDetail="{{orderDetail}}"
    specialNoteData="{{specialNoteData}}"
    bind:showSpecialNote="showSpecialNote"
  />

  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <!-- 底部操作按钮组件 -->
  <bottom-actions
    orderDetail="{{orderDetail}}"
    bind:contactCustomer="contactCustomer"
    bind:viewReview="viewReview"
  />

  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
    hasBottomNavigation="{{false}}"
  />

  <!-- 拒绝原因输入模态框 -->
  <view class="reject-modal" wx:if="{{showRejectModal}}">
    <view class="modal-mask" bindtap="cancelReject"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">拒绝原因</text>
      </view>
      <view class="modal-body">
        <textarea
          class="reject-textarea"
          placeholder="请输入拒绝原因..."
          value="{{rejectReason}}"
          bindinput="onRejectReasonInput"
          maxlength="200"
        ></textarea>
        <view class="char-count">{{rejectReason.length}}/200</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelReject">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmReject">确认拒绝</button>
      </view>
    </view>
  </view>

  <!-- 特殊情况说明组件 -->
  <special-note-upload
    wx:if="{{showSpecialNote}}"
    show="{{showSpecialNote}}"
    orderInfo="{{orderDetail}}"
    readonly="{{specialNoteReadonly}}"
    noteData="{{specialNoteData}}"
    bind:confirm="onSpecialNoteConfirm"
    bind:cancel="onSpecialNoteCancel"
    bind:delete="onSpecialNoteDelete"
  />

  <!-- 地址编辑器组件 -->
  <address-editor
    wx:if="{{showAddressEditor}}"
    show="{{showAddressEditor}}"
    orderInfo="{{orderDetail}}"
    bind:confirm="onAddressEditConfirm"
    bind:cancel="onAddressEditCancel"
  />
</view>